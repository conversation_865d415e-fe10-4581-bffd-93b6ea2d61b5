{"name": "obsidian-ghost-sync", "version": "1.0.0", "description": "Sync posts between Obsidian and Ghost.io", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:legacy": "node test-content-conversion.js", "test:build": "npm test && npm run build", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^16.11.6", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "obsidian": "latest", "ts-jest": "^29.4.1", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"turndown": "^7.2.0"}}