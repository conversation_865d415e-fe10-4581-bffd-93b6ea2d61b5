// Jest setup file for Obsidian plugin testing

// Mock crypto for JWT token generation
if (!global.crypto) {
  Object.defineProperty(global, 'crypto', {
    value: {
      subtle: {
        importKey: jest.fn().mockResolvedValue({}),
        sign: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
      }
    }
  });
}

// Mock btoa/atob for base64 encoding
if (!global.btoa) {
  Object.defineProperty(global, 'btoa', {
    value: (str: string) => Buffer.from(str).toString('base64')
  });
}

if (!global.atob) {
  Object.defineProperty(global, 'atob', {
    value: (str: string) => Buffer.from(str, 'base64').toString()
  });
}
